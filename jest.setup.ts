import { headMock } from '@mocks/next/head.mock';
import { imageMock } from '@mocks/next/image.mock';
import { intlMock } from '@mocks/next/intl.mock';
import { routerMock } from '@mocks/next/router.mock';

import '@testing-library/jest-dom';

// Setup mocks for Next.js components and hooks
jest.mock('next/router', () => routerMock);
jest.mock('next/image', () => imageMock);
jest.mock('next/head', () => headMock);
jest.mock('next-intl', () => intlMock);
