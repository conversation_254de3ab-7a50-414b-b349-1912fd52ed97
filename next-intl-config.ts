import { getRequestConfig } from 'next-intl/server';

type Messages = Record<string, unknown>;
type NamespaceMessages = Record<string, Messages>;

export default getRequestConfig(async ({ locale }) => {
  const currentLocale = locale || 'en';

  const loadNamespace = async (namespace: string, localeToLoad: string): Promise<Messages> => {
    try {
      const moduleImport = (await import(`./src/messages/${localeToLoad}/${namespace}.json`)) as {
        default: Messages;
      };

      return moduleImport.default;
    } catch {
      return {};
    }
  };

  const namespaces = ['header', 'footer', 'home', 'common'];

  const messages: NamespaceMessages = {};

  for (const namespace of namespaces) {
    let namespaceMessages = await loadNamespace(namespace, currentLocale);

    if (Object.keys(namespaceMessages).length === 0 && currentLocale !== 'en') {
      namespaceMessages = await loadNamespace(namespace, 'en');
    }

    messages[namespace] = namespaceMessages;
  }

  return {
    locale: currentLocale,
    messages,
  };
});
