# Pepper - LifeWave AI Frontend

<div align="center">
  <img src="public/pepperLogo.svg" alt="Pepper Logo" width="240" height="85" />

**The gateway to harnessing the power of artificial intelligence within LifeWave**

[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![Next.js](https://img.shields.io/badge/Next.js-000000?style=for-the-badge&logo=next.js&logoColor=white)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-20232A?style=for-the-badge&logo=react&logoColor=61DAFB)](https://reactjs.org/)
[![Emotion](https://img.shields.io/badge/Emotion-DB7093?style=for-the-badge&logo=emotion&logoColor=white)](https://emotion.sh/)
[![Jest](https://img.shields.io/badge/Jest-C21325?style=for-the-badge&logo=jest&logoColor=white)](https://jestjs.io/)

</div>

## 🚀 About Pepper

Welcome to **Pepper**, LifeWave's AI-powered frontend application. This project serves as your gateway to harnessing the
power of artificial intelligence within our company. Here you'll find resources, tools, and updates to help you leverage
AI in your daily tasks and propel LifeWave to the forefront of innovation in our industry.

### 🎯 Purpose

Pepper is designed to:

- **Streamline AI Integration**: Provide easy access to AI tools and resources
- **Solve Business Problems**: Connect business challenges with AI solutions
- **Drive Innovation**: Enable LifeWave to stay at the cutting edge of AI technology
- **Enhance Productivity**: Help teams leverage AI for improved efficiency

## 📋 Prerequisites

Before you begin, ensure you have the following installed on your system:

- **Node.js**: Latest LTS version (as specified in `.nvmrc`)
- **npm**: Latest version (comes with Node.js)
- **Git**: For version control and Git hooks

### Recommended Tools

- **Modern IDE/Editor** with support for:
  - ESLint integration
  - Prettier formatting
  - TypeScript and JavaScript support
  - React and Next.js features
  - Git integration and commit message validation

## 🛠️ Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/LifeWave/pepper-frontend.git
cd pepper-frontend
```

### 2. Install Dependencies

```bash
npm install
```

This will automatically:

- Install all project dependencies
- Set up Husky Git hooks via the `prepare` script
- Configure pre-commit and pre-push hooks

### 3. Start Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

### 4. Verify Setup

Run the following commands to ensure everything is working:

```bash
# Check linting
npm run lint

# Run tests
npm test

# Check formatting
npm run format:check
```

## 📁 Project Structure

```
src/
├── __mocks__/                    # Jest mocks for testing
│   └── next/                     # Next.js specific mocks
├── app/                          # Next.js App Router
│   ├── layout.tsx                # Main application layout
│   └── page.tsx                  # Main page
├── components/                   # Application-specific components
│   ├── Example/                  # Component folder structure
│   │   ├── Example.tsx           # Component code
│   │   ├── Example.test.tsx      # Component tests
│   │   ├── Example.helpers.ts    # Component helpers & test IDs
│   │   ├── Example.styles.ts     # Component styles
│   │   └── Example.types.ts      # Component types
├── hooks/                        # Custom React hooks
├── lib/                          # Libraries and utilities
│   └── emotion.tsx               # Emotion configuration
├── messages/                     # Internationalization files
│   ├── en/                       # English translations
│   └── es/                       # Spanish translations
├── styles/                       # Global styles
│   └── GlobalStyles.tsx          # Global styles
├── theme/                        # Theme configuration
│   └── theme.ts                  # Main theme
└── utils/                        # Utility functions
    ├── styled.ts                 # Styling utilities
    └── test-utils.tsx            # Testing utilities
```

## 🏗️ Technology Stack

Pepper is built with modern, industry-leading technologies to ensure scalability, maintainability, and developer
experience:

### 🎯 Core Framework & Runtime

| Technology                                        | Purpose                                                                       |
| ------------------------------------------------- | ----------------------------------------------------------------------------- |
| **[Next.js](https://nextjs.org/docs)**            | React framework with App Router, Server Components, and optimized performance |
| **[React](https://reactjs.org/)**                 | UI library for building interactive user interfaces                           |
| **[TypeScript](https://www.typescriptlang.org/)** | Static typing for enhanced developer experience and code quality              |
| **[Node.js](https://nodejs.org/)**                | JavaScript runtime environment                                                |

### 🎨 Styling & UI

| Technology                         | Purpose                                                    |
| ---------------------------------- | ---------------------------------------------------------- |
| **[Emotion](https://emotion.sh/)** | CSS-in-JS library for component styling with theme support |

### 🌐 Internationalization

| Technology                              | Purpose                                                             |
| --------------------------------------- | ------------------------------------------------------------------- |
| **[next-intl](https://next-intl.dev/)** | Internationalization with namespace support for English and Spanish |

### 🧪 Testing Framework

| Technology                                                           | Purpose                                                  |
| -------------------------------------------------------------------- | -------------------------------------------------------- |
| **[Jest](https://jestjs.io/)**                                       | JavaScript testing framework with 90% coverage threshold |
| **[React Testing Library](https://testing-library.com/)**            | Component testing focused on user behavior               |
| **[Jest DOM](https://github.com/testing-library/jest-dom)**          | Custom Jest matchers for DOM testing                     |
| **[User Event](https://testing-library.com/docs/user-event/intro/)** | Simulating user interactions in tests                    |

### 🔧 Development Tools & Quality Assurance

| Technology                                     | Purpose                                                         |
| ---------------------------------------------- | --------------------------------------------------------------- |
| **[ESLint](https://eslint.org/)**              | JavaScript/TypeScript linting with custom rules and auto-fixing |
| **[Prettier](https://prettier.io/)**           | Code formatting with JSON key sorting plugin                    |
| **[Stylelint](https://stylelint.io/)**         | CSS-in-JS linting for consistent styling patterns               |
| **[Husky](https://typicode.github.io/husky/)** | Git hooks management for automated quality checks               |
| **[Commitlint](https://commitlint.js.org/)**   | Conventional commit message validation                          |

### 📦 ESLint Plugins & Extensions

| Plugin                               | Purpose                             |
| ------------------------------------ | ----------------------------------- |
| **eslint-plugin-react-hooks**        | React Hooks rules enforcement       |
| **eslint-plugin-simple-import-sort** | Automatic import sorting            |
| **eslint-plugin-unused-imports**     | Remove unused imports automatically |
| **eslint-plugin-sonarjs**            | Code quality and bug detection      |
| **eslint-plugin-prettier**           | Prettier integration with ESLint    |
| **typescript-eslint**                | TypeScript-specific linting rules   |

### 🔄 Automation Features

- **Pre-commit Hooks**: Automatic linting, formatting, and style checking
- **Pre-push Hooks**: Automated test execution before code push
- **JSON Sorting**: Automatic alphabetical sorting of JSON object keys
- **Import Sorting**: Automatic organization of import statements
- **Code Coverage**: 90% threshold enforcement across the codebase

## 📜 Available Scripts

### 🚀 Development Commands

| Command         | Description                                 |
| --------------- | ------------------------------------------- |
| `npm run dev`   | Start the development server with Turbopack |
| `npm run build` | Build the application for production        |
| `npm run start` | Start the application in production mode    |

### 🧹 Code Quality Commands

| Command                | Description                                                |
| ---------------------- | ---------------------------------------------------------- |
| `npm run lint`         | Run complete linting suite (Prettier + ESLint + Stylelint) |
| `npm run lint:check`   | Check for ESLint issues without fixing                     |
| `npm run lint:fix`     | Fix ESLint issues automatically                            |
| `npm run lint:style`   | Fix Stylelint issues for CSS-in-JS                         |
| `npm run format`       | Format all files with Prettier                             |
| `npm run format:check` | Check if files are properly formatted                      |

### 🧪 Testing Commands

| Command                     | Description                                    |
| --------------------------- | ---------------------------------------------- |
| `npm test`                  | Run all tests once                             |
| `npm run test:watch`        | Run tests in watch mode for development        |
| `npm run test:coverage`     | Run tests with coverage report (90% threshold) |
| `npm run test:clear-cache`  | Clear Jest cache                               |
| `npm run test:update-cache` | Update Jest snapshots                          |
| `npm run test:debug`        | Debug tests with Node inspector                |

### 🔗 Git Hooks Commands

| Command              | Description                                               |
| -------------------- | --------------------------------------------------------- |
| `npm run prepare`    | Set up Husky Git hooks (runs automatically after install) |
| `npm run pre-commit` | Run linting suite (used by pre-commit hook)               |
| `npm run pre-push`   | Run tests (used by pre-push hook)                         |

### 🎯 Quick Development Workflow

```bash
# Start development
npm run dev

# In another terminal, run tests in watch mode
npm run test:watch

# Before committing (automatic via Git hooks)
npm run lint
npm test
```

## 🔗 Git Hooks & Quality Gates

This project uses [Husky](https://typicode.github.io/husky/) to enforce code quality automatically through Git hooks:

### 🚦 Automated Quality Checks

| Hook           | Trigger            | Actions                             | Purpose                                  |
| -------------- | ------------------ | ----------------------------------- | ---------------------------------------- |
| **Pre-commit** | Before each commit | Linting + Formatting + Style checks | Ensure code quality before commit        |
| **Pre-push**   | Before each push   | Full test suite execution           | Prevent broken code from reaching remote |
| **Commit-msg** | On commit message  | Conventional commit validation      | Maintain consistent commit history       |

### 📝 Pre-commit Hook Details

When you run `git commit`, the following happens automatically:

1. **Prettier Formatting** (`npm run format`)

   - Formats all code files
   - Sorts JSON object keys alphabetically
   - Ensures consistent code style

2. **ESLint Fixing** (`npm run lint:fix`)

   - Fixes auto-fixable linting issues
   - Enforces TypeScript best practices
   - Sorts imports automatically

3. **Stylelint Fixing** (`npm run lint:style`)
   - Fixes CSS-in-JS styling issues
   - Ensures consistent styling patterns

### 🧪 Pre-push Hook Details

When you run `git push`, the following happens automatically:

- **Full Test Suite** (`npm run test`)
  - Runs all unit tests
  - Enforces 90% code coverage threshold
  - Prevents push if any tests fail

### 💬 Commit Message Validation

All commit messages must follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

**Format**: `type(scope): description`

#### ✅ Allowed Commit Types

| Type       | Purpose                  | Example                                     |
| ---------- | ------------------------ | ------------------------------------------- |
| `feat`     | New feature              | `feat: add user authentication`             |
| `fix`      | Bug fix                  | `fix: resolve memory leak in component`     |
| `docs`     | Documentation            | `docs: update installation instructions`    |
| `style`    | Code style changes       | `style: format code with prettier`          |
| `refactor` | Code refactoring         | `refactor: extract utility function`        |
| `perf`     | Performance improvements | `perf: optimize image loading`              |
| `test`     | Adding/updating tests    | `test: add unit tests for header component` |
| `build`    | Build system changes     | `build: update webpack configuration`       |
| `ci`       | CI/CD changes            | `ci: add GitHub Actions workflow`           |
| `chore`    | Maintenance tasks        | `chore: update dependencies`                |
| `revert`   | Revert previous commit   | `revert: undo feature X implementation`     |

#### 📏 Message Rules

- **Header**: Maximum 100 characters
- **Type**: Required, lowercase
- **Scope**: Optional, lowercase
- **Description**: Required, no period at the end
- **Body**: Optional, maximum 100 characters per line
- **Footer**: Optional, maximum 100 characters per line

## 📋 Code Conventions & Standards

### 🏗️ Component Architecture

#### Component Structure

Each component follows a consistent folder structure:

```
src/components/ComponentName/
├── ComponentName.tsx           # Main component logic
├── ComponentName.test.tsx      # Unit tests
├── ComponentName.styles.ts     # Emotion styled components
├── ComponentName.types.ts      # TypeScript interfaces
└── ComponentName.helpers.ts    # Helper functions and test IDs
```

#### Component Guidelines

- **Use functional components** with React Hooks
- **Single responsibility**: Each component should have one clear purpose
- **Composition over inheritance**: Prefer component composition
- **Props interface**: Always define TypeScript interfaces for props

### 🎨 Styling Standards

- **CSS-in-JS**: Use Emotion for all styling
- **Theme system**: Utilize the centralized theme for consistency
- **Responsive design**: Use theme breakpoints for mobile-first approach
- **Styled components**: Create reusable styled components

### 📦 Import Organization

Imports are automatically sorted by `eslint-plugin-simple-import-sort` in this order:

1. **React & Core Libraries**
2. **Next.js specific imports**
3. **Third-party libraries**
4. **Internal modules** (using path aliases)
5. **Relative imports**

#### Path Aliases

Use these aliases to avoid relative imports:

| Alias           | Path               | Purpose                |
| --------------- | ------------------ | ---------------------- |
| `@components/*` | `src/components/*` | React components       |
| `@hooks/*`      | `src/hooks/*`      | Custom React hooks     |
| `@lib/*`        | `src/lib/*`        | Library configurations |
| `@styles/*`     | `src/styles/*`     | Global styles          |
| `@theme/*`      | `src/theme/*`      | Theme configuration    |
| `@utils/*`      | `src/utils/*`      | Utility functions      |
| `@mocks/*`      | `src/__mocks__/*`  | Test mocks             |

### 🔤 Naming Conventions

- **Components**: PascalCase (`UserProfile`, `NavigationMenu`)
- **Files**: PascalCase for components, camelCase for utilities
- **Variables**: camelCase (`userName`, `isLoading`)
- **Constants**: UPPER_SNAKE_CASE (`API_BASE_URL`, `MAX_RETRY_COUNT`)
- **Types/Interfaces**: PascalCase with descriptive names (`UserProfileProps`, `ApiResponse`)

## 🌐 Internationalization (i18n)

Pepper supports multiple languages using [next-intl](https://next-intl.dev/) with namespace-based organization:

### 🗣️ Supported Languages

- **English (en)**: Default language
- **Spanish (es)**: Secondary language with fallback to English

### 📁 Message File Structure

Translation files are organized by namespace for better maintainability:

```
src/messages/
├── en/                         # English translations
│   ├── common.json            # Shared translations
│   ├── header.json            # Header component translations
│   ├── footer.json            # Footer component translations
│   └── home.json              # Home page translations
└── es/                         # Spanish translations
    ├── common.json            # Shared translations
    ├── header.json            # Header component translations
    ├── footer.json            # Footer component translations
    └── home.json              # Home page translations
```

### 🔧 Configuration Features

- **Automatic locale detection**: Browser language detection with fallback
- **Namespace support**: Dynamic namespace loading without configuration changes
- **Fallback mechanism**: Missing Spanish translations fall back to English
- **Server-side rendering**: Full SSR support with Next.js App Router

### 💻 Usage in Components

```tsx
import { useTranslations } from 'next-intl';

export const MyComponent = () => {
  const t = useTranslations('header'); // Load header namespace

  return (
    <div>
      <h1>{t('title')}</h1>
      <p>{t('description')}</p>
    </div>
  );
};
```

### 📝 Adding New Translations

1. **Add to English** (`src/messages/en/namespace.json`)
2. **Add to Spanish** (`src/messages/es/namespace.json`)
3. **Use in component** with `useTranslations('namespace')`

The namespace system automatically loads new translation files without requiring configuration updates.

## 🧪 Testing

This project uses Jest and React Testing Library for comprehensive testing with a 90% coverage threshold.

### 🎯 Testing Philosophy

- **Test behavior, not implementation**: Focus on what the component does, not how it does it
- **User-centric testing**: Tests should resemble how users interact with the application
- **Maintainable tests**: Write tests that are easy to understand and maintain

### 📁 Test Structure

Tests are co-located with components:

```
src/components/Header/
├── Header.tsx
├── Header.test.tsx      # Unit tests
├── Header.helpers.ts    # Test IDs and helper functions
├── Header.styles.ts
└── Header.types.ts
```

### 🔧 Test Utilities

#### Test IDs Organization

Define test IDs in component helpers:

```tsx
// Header.helpers.ts
export const HeaderTestIds = {
  container: 'header-container',
  logo: 'header-logo',
  navigation: 'header-navigation',
};
```

#### Custom Render Function

Use the custom render function for Emotion support:

```tsx
import { render, screen } from '@utils/test-utils';
import { Header } from './Header';
import { HeaderTestIds } from './Header.helpers';

describe('Header', () => {
  it('should render correctly', () => {
    render(<Header />);

    expect(screen.getByTestId(HeaderTestIds.container)).toBeInTheDocument();
  });
});
```

### 🎭 Mocking Strategy

#### Next.js Mocks

Located in `src/__mocks__/next/`:

- **Image**: Mock Next.js Image component
- **Router**: Mock Next.js router functionality
- **Intl**: Mock next-intl internationalization
- **Head**: Mock Next.js Head component

#### Usage Example

```tsx
// Mocks are automatically applied via jest.setup.ts
import { render } from '@utils/test-utils';
import { MyComponent } from './MyComponent';

// next/image, next/router, etc. are automatically mocked
```

### 📊 Coverage Requirements

- **Statements**: 90%
- **Branches**: 90%
- **Functions**: 90%
- **Lines**: 90%

Excluded from coverage:

- `src/app/**` (Next.js App Router files)
- `src/lib/**` (Library configurations)
- `src/styles/**` (Global styles)

## 🚀 Deployment

### 🏗️ Production Build

```bash
npm run build
npm run start
```

### ☁️ Vercel Deployment

Deploy easily using [Vercel Platform](https://vercel.com/new):

**Features:**

- **Git Integration**: Automatic deployments on push to main
- **Preview Deployments**: Every PR gets a preview URL
- **Environment Variables**: Secure variable management
- **Edge Functions**: Global edge network for optimal performance

For more deployment options, see [Next.js deployment documentation](https://nextjs.org/docs/deployment).

## 👥 Contributing

### 🔄 Development Workflow

1. **Fork & Clone**: Fork the repository and clone your fork
2. **Install Dependencies**: Run `npm install`
3. **Create Branch**: Create a feature branch (`git checkout -b feature/amazing-feature`)
4. **Develop**: Make your changes following the code conventions
5. **Test**: Ensure all tests pass (`npm test`)
6. **Commit**: Use conventional commit messages
7. **Push**: Push to your fork (`git push origin feature/amazing-feature`)
8. **Pull Request**: Create a pull request with a clear description

### ✅ Code Review Process

All contributions go through code review:

- **Automated Checks**: Pre-commit hooks ensure code quality
- **Test Coverage**: Maintain 90% test coverage
- **Documentation**: Update documentation for new features (if necessary)
- **Conventional Commits**: Follow commit message standards

### 🆘 Getting Help

- **Issues**: Report bugs or request features via GitHub Issues
- **Discussions**: Join project discussions for questions and ideas
- **AI Team**: Submit AI Challenge forms for business problem solutions

## 📄 License

This project is proprietary software owned by LifeWave. All rights reserved.

## 🙏 Acknowledgments

- **LifeWave AI Team**: For driving innovation and AI integration
- **Next.js Team**: For the amazing React framework
- **Vercel**: For the deployment platform
- **Open Source Community**: For the incredible tools and libraries

---

<div align="center">
  <p><strong>Built with ❤️ by the LifeWave AI Team</strong></p>
  <p>Empowering LifeWave with cutting-edge AI technology</p>
</div>
