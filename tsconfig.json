{"compilerOptions": {"allowJs": true, "esModuleInterop": true, "incremental": true, "isolatedModules": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "moduleResolution": "bundler", "noEmit": true, "paths": {"@components/*": ["./src/components/*"], "@hooks/*": ["./src/hooks/*"], "@lib/*": ["./src/lib/*"], "@mocks/*": ["./src/__mocks__/*"], "@store/*": ["./src/store/*"], "@styles/*": ["./src/styles/*"], "@theme/*": ["./src/theme/*"], "@utils/*": ["./src/utils/*"]}, "plugins": [{"name": "next"}], "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "ES2017", "types": ["jest", "@testing-library/jest-dom"]}, "exclude": ["node_modules"], "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"]}