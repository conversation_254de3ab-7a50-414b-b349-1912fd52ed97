import { ReactElement } from 'react';

import { ThemeProvider } from '@emotion/react';

import {
  fireEvent,
  render as rtlRender,
  RenderOptions,
  screen,
  waitFor,
} from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { theme } from '@theme/theme';

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
): ReturnType<typeof rtlRender> => {
  const AllProviders = ({ children }: { children: React.ReactNode }) => {
    return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
  };

  return rtlRender(ui, { wrapper: AllProviders, ...options });
};

export { fireEvent, customRender as render, screen, userEvent, waitFor };
