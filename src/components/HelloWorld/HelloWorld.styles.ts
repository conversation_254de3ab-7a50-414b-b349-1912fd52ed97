import styled from '@emotion/styled';

import { theme } from '@theme/theme';

export const HelloWorldContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${theme.space.medium};
  padding: ${theme.space.large};
  border-radius: ${theme.radii.medium};
  background-color: ${theme.colors.gray[100]};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 0 auto;
`;

export const MessageText = styled.h1`
  font-family: ${theme.fonts.body};
  font-size: ${theme.fontSizes.large};
  color: ${theme.colors.primary};
  margin: 0;
  text-align: center;
`;

export const TimestampText = styled.p`
  font-family: ${theme.fonts.mono};
  font-size: ${theme.fontSizes.small};
  color: ${theme.colors.text};
  margin: 0;
  opacity: 0.7;
`;

export const LoadingText = styled.p`
  font-family: ${theme.fonts.body};
  font-size: ${theme.fontSizes.medium};
  color: ${theme.colors.text};
  margin: 0;
  animation: pulse 1.5s ease-in-out infinite;

  @keyframes pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }
`;

export const ErrorText = styled.p`
  font-family: ${theme.fonts.body};
  font-size: ${theme.fontSizes.medium};
  color: ${theme.colors.error};
  margin: 0;
  text-align: center;
`;
