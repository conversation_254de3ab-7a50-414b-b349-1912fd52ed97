'use client';

import { FC } from 'react';

import { useGetHelloWorldQuery } from '../../store/api/apiSlice';

import { HelloWorldTestIds } from '@components/HelloWorld/HelloWorld.helpers';
import {
  ErrorText,
  HelloWorldContainer,
  LoadingText,
  MessageText,
  TimestampText,
} from '@components/HelloWorld/HelloWorld.styles';
import { HelloWorldProps } from '@components/HelloWorld/HelloWorld.types';

export const HelloWorld: FC<HelloWorldProps> = ({ className }) => {
  const {
    data: helloData,
    error,
    isLoading,
    isError,
  } = useGetHelloWorldQuery(undefined, {
    // Refetch every 30 seconds
    pollingInterval: 30000,
    // Refetch on focus
    refetchOnFocus: true,
    // Refetch on reconnect
    refetchOnReconnect: true,
  });

  const renderContent = () => {
    if (isLoading) {
      return (
        <LoadingText data-testid={HelloWorldTestIds.loading}>
          Cargando mensaje desde la API...
        </LoadingText>
      );
    }

    if (isError) {
      return (
        <ErrorText data-testid={HelloWorldTestIds.error}>
          Error al cargar el mensaje: {error && 'data' in error ? String(error.data) : 'Error desconocido'}
        </ErrorText>
      );
    }

    if (helloData) {
      return (
        <>
          <MessageText data-testid={HelloWorldTestIds.message}>
            {helloData.message}
          </MessageText>
          {helloData.timestamp && (
            <TimestampText data-testid={HelloWorldTestIds.timestamp}>
              Última actualización: {new Date(helloData.timestamp).toLocaleString('es-ES')}
            </TimestampText>
          )}
        </>
      );
    }

    return null;
  };

  return (
    <HelloWorldContainer className={className} data-testid={HelloWorldTestIds.container}>
      {renderContent()}
    </HelloWorldContainer>
  );
};
