// Mock the API slice
jest.mock('@store/api/apiSlice', () => ({
  useGetHelloWorldQuery: jest.fn(),
}));

import { HelloWorld } from '@components/HelloWorld/HelloWorld';
import { HelloWorldTestIds } from '@components/HelloWorld/HelloWorld.helpers';

import { render, screen, waitFor } from '@utils/test-utils';

const mockUseGetHelloWorldQuery = jest.mocked(
  require('@store/api/apiSlice').useGetHelloWorldQuery
);

describe('HelloWorld component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should display loading state', () => {
    mockUseGetHelloWorldQuery.mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: true,
      isError: false,
    });

    render(<HelloWorld />);

    const loadingElement = screen.getByTestId(HelloWorldTestIds.loading);
    expect(loadingElement).toBeInTheDocument();
    expect(loadingElement).toHaveTextContent('Cargando mensaje desde la API...');
  });

  it('should display error state', () => {
    const mockError = {
      status: 500,
      data: 'Internal Server Error',
    };

    mockUseGetHelloWorldQuery.mockReturnValue({
      data: undefined,
      error: mockError,
      isLoading: false,
      isError: true,
    });

    render(<HelloWorld />);

    const errorElement = screen.getByTestId(HelloWorldTestIds.error);
    expect(errorElement).toBeInTheDocument();
    expect(errorElement).toHaveTextContent('Error al cargar el mensaje');
  });

  it('should display hello world message when data is loaded', async () => {
    const mockData = {
      message: 'Hello World!',
      timestamp: '2024-01-01T12:00:00.000Z',
    };

    mockUseGetHelloWorldQuery.mockReturnValue({
      data: mockData,
      error: undefined,
      isLoading: false,
      isError: false,
    });

    render(<HelloWorld />);

    await waitFor(() => {
      const messageElement = screen.getByTestId(HelloWorldTestIds.message);
      expect(messageElement).toBeInTheDocument();
      expect(messageElement).toHaveTextContent('Hello World!');
    });

    const timestampElement = screen.getByTestId(HelloWorldTestIds.timestamp);
    expect(timestampElement).toBeInTheDocument();
  });

  it('should display message without timestamp when timestamp is not provided', async () => {
    const mockData = {
      message: 'Hello World!',
    };

    mockUseGetHelloWorldQuery.mockReturnValue({
      data: mockData,
      error: undefined,
      isLoading: false,
      isError: false,
    });

    render(<HelloWorld />);

    await waitFor(() => {
      const messageElement = screen.getByTestId(HelloWorldTestIds.message);
      expect(messageElement).toBeInTheDocument();
      expect(messageElement).toHaveTextContent('Hello World!');
    });

    const timestampElement = screen.queryByTestId(HelloWorldTestIds.timestamp);
    expect(timestampElement).not.toBeInTheDocument();
  });

  it('should have correct container structure', () => {
    mockUseGetHelloWorldQuery.mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: true,
      isError: false,
    });

    render(<HelloWorld />);

    const containerElement = screen.getByTestId(HelloWorldTestIds.container);
    expect(containerElement).toBeInTheDocument();
  });
});
