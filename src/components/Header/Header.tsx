'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

import { HeaderTestIds } from '@components/Header/Header.helpers';
import { HeaderContainer, Logo, Nav, NavLink } from '@components/Header/Header.styles';

export const Header = () => {
  const t = useTranslations('header');

  return (
    <HeaderContainer data-testid={HeaderTestIds.container}>
      <Logo data-testid={HeaderTestIds.logoContainer}>
        <Link href="/">
          <Image
            priority
            alt="Logo"
            data-testid={HeaderTestIds.logoImage}
            height={32}
            src="/next.svg"
            style={{ objectFit: 'contain' }}
            width={120}
          />
        </Link>
      </Logo>
      <Nav data-testid={HeaderTestIds.navContainer}>
        <NavLink data-testid={HeaderTestIds.homeLink} href="/">
          {t('home')}
        </NavLink>
        <NavLink data-testid={HeaderTestIds.aboutLink} href="/about">
          {t('about')}
        </NavLink>
        <NavLink data-testid={HeaderTestIds.contactLink} href="/contact">
          {t('contact')}
        </NavLink>
      </Nav>
    </HeaderContainer>
  );
};
