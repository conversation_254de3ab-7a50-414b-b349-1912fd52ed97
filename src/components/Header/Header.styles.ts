import Link from 'next/link';

import styled from '@emotion/styled';

export const HeaderContainer = styled.header`
  align-items: center;
  background-color: #fff;
  border-bottom: 0.0625rem solid rgb(0 0 0 / 5%);
  display: flex;
  justify-content: space-between;
  padding: 1rem 1.5rem;

  @media (prefers-color-scheme: dark) {
    background-color: #000;
    border-bottom-color: rgb(255 255 255 / 6%);
  }

  @media (width <= 37.5rem) {
    padding: 0.75rem 1rem;
  }
`;

export const Logo = styled.div`
  height: 2rem;
  position: relative;
  width: 7.5rem;

  @media (prefers-color-scheme: dark) {
    img {
      filter: invert(1);
    }
  }
`;

export const Nav = styled.nav`
  display: flex;
  gap: 1.5rem;

  @media (width <= 37.5rem) {
    gap: 1rem;
  }
`;

export const NavLink = styled(Link)`
  color: #000;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s ease;

  &:hover {
    color: #0070f3;
    text-decoration: none;
  }

  @media (prefers-color-scheme: dark) {
    color: #fff;
  }
`;
