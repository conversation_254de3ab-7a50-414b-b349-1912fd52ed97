import { Header } from '@components/Header/Header';
import { HeaderTestIds } from '@components/Header/Header.helpers';

import { render, screen } from '@utils/test-utils';

describe('Header component', () => {
  it('should render the logo', () => {
    render(<Header />);

    const logoImage = screen.getByTestId(HeaderTestIds.logoImage);
    expect(logoImage).toBeInTheDocument();
    expect(logoImage).toHaveAttribute('alt', 'Logo');
  });

  it('should render navigation links', () => {
    render(<Header />);

    const homeLink = screen.getByTestId(HeaderTestIds.homeLink);
    const aboutLink = screen.getByTestId(HeaderTestIds.aboutLink);
    const contactLink = screen.getByTestId(HeaderTestIds.contactLink);

    expect(homeLink).toBeInTheDocument();
    expect(homeLink).toHaveAttribute('href', '/');

    expect(aboutLink).toBeInTheDocument();
    expect(aboutLink).toHaveAttribute('href', '/about');

    expect(contactLink).toBeInTheDocument();
    expect(contactLink).toHaveAttribute('href', '/contact');
  });
});
