import { Footer } from '@components/Footer/Footer';
import { FooterTestIds } from '@components/Footer/Footer.helpers';

import { render, screen } from '@utils/test-utils';

describe('Footer component', () => {
  it('should render the footer container', () => {
    render(<Footer />);

    const footerElement = screen.getByTestId(FooterTestIds.container);
    expect(footerElement).toBeInTheDocument();
  });

  it('should render the Resources section with links', () => {
    render(<Footer />);

    const resourcesTitle = screen.getByTestId(FooterTestIds.resourcesTitle);
    expect(resourcesTitle).toBeInTheDocument();

    const documentationLink = screen.getByTestId(FooterTestIds.documentationLink);
    const learnLink = screen.getByTestId(FooterTestIds.learnLink);
    const templatesLink = screen.getByTestId(FooterTestIds.templatesLink);

    expect(documentationLink).toBeInTheDocument();
    expect(documentationLink).toHaveAttribute('href', 'https://nextjs.org/docs');

    expect(learnLink).toBeInTheDocument();
    expect(learnLink).toHaveAttribute('href', 'https://nextjs.org/learn');

    expect(templatesLink).toBeInTheDocument();
    expect(templatesLink).toHaveAttribute('href', 'https://vercel.com/templates');
  });

  it('should render the More section with links', () => {
    render(<Footer />);

    const moreTitle = screen.getByTestId(FooterTestIds.moreTitle);
    expect(moreTitle).toBeInTheDocument();

    const githubLink = screen.getByTestId(FooterTestIds.githubLink);
    const blogLink = screen.getByTestId(FooterTestIds.blogLink);
    const nextjsLink = screen.getByTestId(FooterTestIds.nextjsLink);

    expect(githubLink).toBeInTheDocument();
    expect(githubLink).toHaveAttribute('href', 'https://github.com/vercel/next.js');

    expect(blogLink).toBeInTheDocument();
    expect(blogLink).toHaveAttribute('href', 'https://nextjs.org/blog');

    expect(nextjsLink).toBeInTheDocument();
    expect(nextjsLink).toHaveAttribute('href', 'https://nextjs.org');
  });

  it('should render the copyright section with current year', () => {
    const originalDate = global.Date;
    const mockDate = class extends Date {
      getFullYear() {
        return 2025;
      }
    };
    global.Date = mockDate as DateConstructor;

    render(<Footer />);

    const copyrightText = screen.getByTestId(FooterTestIds.copyright);
    expect(copyrightText).toBeInTheDocument();

    global.Date = originalDate;
  });

  it('should render all images with correct attributes', () => {
    render(<Footer />);

    const fileIcons = screen.getAllByTestId(FooterTestIds.fileIcon);
    expect(fileIcons).toHaveLength(2);
    fileIcons.forEach((icon) => {
      expect(icon).toHaveAttribute('src', '/file.svg');
      expect(icon).toHaveAttribute('width', '16');
      expect(icon).toHaveAttribute('height', '16');
      expect(icon).toHaveAttribute('aria-hidden', 'true');
    });

    const windowIcon = screen.getByTestId(FooterTestIds.windowIcon);
    expect(windowIcon).toHaveAttribute('src', '/window.svg');
    expect(windowIcon).toHaveAttribute('width', '16');
    expect(windowIcon).toHaveAttribute('height', '16');
    expect(windowIcon).toHaveAttribute('aria-hidden', 'true');

    const globeIcon = screen.getByTestId(FooterTestIds.globeIcon);
    expect(globeIcon).toHaveAttribute('src', '/globe.svg');
    expect(globeIcon).toHaveAttribute('width', '16');
    expect(globeIcon).toHaveAttribute('height', '16');
    expect(globeIcon).toHaveAttribute('aria-hidden', 'true');
  });
});
