'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';

import { FooterTestIds } from '@components/Footer/Footer.helpers';
import {
  Copyright,
  FooterContainer,
  FooterLink,
  FooterLinks,
  FooterSection,
  FooterTitle,
} from '@components/Footer/Footer.styles';

export const Footer = () => {
  const t = useTranslations('footer');
  const tCommon = useTranslations('common');

  return (
    <FooterContainer data-testid={FooterTestIds.container}>
      <FooterSection>
        <FooterTitle data-testid={FooterTestIds.resourcesTitle}>{t('resources')}</FooterTitle>
        <FooterLinks>
          <FooterLink data-testid={FooterTestIds.documentationLink} href="https://nextjs.org/docs">
            <Image
              aria-hidden
              alt="File icon"
              data-testid={FooterTestIds.fileIcon}
              height={16}
              src="/file.svg"
              width={16}
            />
            {t('documentation')}
          </FooterLink>
          <FooterLink data-testid={FooterTestIds.learnLink} href="https://nextjs.org/learn">
            <Image
              aria-hidden
              alt="File icon"
              data-testid={FooterTestIds.fileIcon}
              height={16}
              src="/file.svg"
              width={16}
            />
            {t('learn')}
          </FooterLink>
          <FooterLink data-testid={FooterTestIds.templatesLink} href="https://vercel.com/templates">
            <Image
              aria-hidden
              alt="Window icon"
              data-testid={FooterTestIds.windowIcon}
              height={16}
              src="/window.svg"
              width={16}
            />
            {t('templates')}
          </FooterLink>
        </FooterLinks>
      </FooterSection>

      <FooterSection>
        <FooterTitle data-testid={FooterTestIds.moreTitle}>{tCommon('more')}</FooterTitle>
        <FooterLinks>
          <FooterLink
            data-testid={FooterTestIds.githubLink}
            href="https://github.com/vercel/next.js"
          >
            {t('github')}
          </FooterLink>
          <FooterLink data-testid={FooterTestIds.blogLink} href="https://nextjs.org/blog">
            {t('blog')}
          </FooterLink>
          <FooterLink data-testid={FooterTestIds.nextjsLink} href="https://nextjs.org">
            <Image
              aria-hidden
              alt="Globe icon"
              data-testid={FooterTestIds.globeIcon}
              height={16}
              src="/globe.svg"
              width={16}
            />
            {t('nextjs')}
          </FooterLink>
        </FooterLinks>
      </FooterSection>

      <FooterSection>
        <Copyright data-testid={FooterTestIds.copyright}>
          {t('copyright', { year: new Date().getFullYear() })}
        </Copyright>
      </FooterSection>
    </FooterContainer>
  );
};
