import Link from 'next/link';

import styled from '@emotion/styled';

export const FooterContainer = styled.footer`
  background-color: #f5f5f5;
  display: flex;
  justify-content: space-between;
  padding: 2rem 1.5rem;

  @media (prefers-color-scheme: dark) {
    background-color: #111;
  }

  @media (width <= 37.5rem) {
    flex-direction: column;
    gap: 1.5rem;
    padding: 1.5rem 1rem;
  }
`;

export const FooterSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

export const FooterTitle = styled.h3`
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
`;

export const FooterLinks = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

export const FooterLink = styled(Link)`
  align-items: center;
  color: #666;
  display: flex;
  font-size: 0.875rem;
  gap: 0.5rem;
  text-decoration: none;
  transition: color 0.2s ease;

  &:hover {
    color: #0070f3;
    text-decoration: underline;
    text-underline-offset: 0.25rem;
  }

  @media (prefers-color-scheme: dark) {
    color: #aaa;
  }
`;

export const Copyright = styled.p`
  color: #666;
  font-size: 0.875rem;
  margin: 0;

  @media (prefers-color-scheme: dark) {
    color: #aaa;
  }
`;
