import { Main } from '@components/Main/Main';
import { MainTestIds } from '@components/Main/Main.helpers';

import { render, screen } from '@utils/test-utils';

describe('Main component', () => {
  it('should render children correctly', () => {
    const testText = 'Test content';

    render(
      <Main>
        <div data-testid="test-child">{testText}</div>
      </Main>
    );

    const childElement = screen.getByTestId('test-child');
    expect(childElement).toBeInTheDocument();
    expect(childElement).toHaveTextContent(testText);
  });

  it('should have the correct structure', () => {
    render(
      <Main>
        <div>Content</div>
      </Main>
    );

    const mainContainer = screen.getByTestId(MainTestIds.mainContainer);
    expect(mainContainer).toBeInTheDocument();
  });
});
