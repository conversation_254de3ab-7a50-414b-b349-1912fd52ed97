import styled from '@emotion/styled';

import { theme } from '@theme/theme';

export const PageContainer = styled.div`
  display: grid;
  gap: 4rem;
  grid-template-rows: 1.25rem 1fr 1.25rem;
  min-height: 100vh;
  padding: ${theme.space.xxlarge};
  place-items: center center;

  @media (max-width: ${theme.breakpoints.mobile}) {
    padding: ${theme.space.large};
    padding-bottom: ${theme.space.xxlarge};
  }
`;

export const MainContainer = styled.main`
  display: flex;
  flex-direction: column;
  gap: 2rem;
  grid-row-start: 2;

  @media (width <= 37.5rem) {
    align-items: center;
  }
`;
