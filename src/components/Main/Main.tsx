'use client';

import { FC } from 'react';

import { MainTestIds } from '@components/Main/Main.helpers';
import { MainContainer, PageContainer } from '@components/Main/Main.styles';
import { MainContentProps } from '@components/Main/Main.types';

export const Main: FC<MainContentProps> = ({ children }) => {
  return (
    <PageContainer data-testid={MainTestIds.pageContainer}>
      <MainContainer data-testid={MainTestIds.mainContainer}>{children}</MainContainer>
    </PageContainer>
  );
};
