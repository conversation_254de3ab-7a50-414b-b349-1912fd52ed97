export const theme = {
  colors: {
    primary: '#0070f3',
    secondary: '#ff4081',
    background: '#ffffff',
    text: '#333333',
    error: '#d32f2f',
    success: '#388e3c',
    gray: {
      100: 'rgba(0, 0, 0, 0.05)',
      200: 'rgba(0, 0, 0, 0.08)',
    },
    dark: {
      gray: {
        100: 'rgba(255, 255, 255, 0.06)',
        200: 'rgba(255, 255, 255, 0.145)',
      },
      buttonPrimaryHover: '#ccc',
      buttonSecondaryHover: '#1a1a1a',
    },
    buttonPrimaryHover: '#383838',
    buttonSecondaryHover: '#f2f2f2',
  },
  fonts: {
    body: 'var(--font-geist-sans)',
    mono: 'var(--font-geist-mono)',
  },
  fontSizes: {
    small: '14px',
    medium: '16px',
    large: '20px',
  },
  space: {
    small: '8px',
    medium: '16px',
    large: '32px',
    xlarge: '64px',
    xxlarge: '80px',
  },
  radii: {
    small: '4px',
    medium: '8px',
    large: '128px',
  },
  breakpoints: {
    mobile: '600px',
  },
};
