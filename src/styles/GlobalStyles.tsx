'use client';

import { css, Global } from '@emotion/react';

import { theme } from '@theme/theme';

const globalStyles = css`
  :root {
    --primary-color: ${theme.colors.primary};
    --secondary-color: ${theme.colors.secondary};
    --background-color: ${theme.colors.background};
    --text-color: ${theme.colors.text};
    --error-color: ${theme.colors.error};
    --success-color: ${theme.colors.success};
    --font-body: ${theme.fonts.body};
  }

  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }

  html,
  body {
    background-color: ${theme.colors.background};
    color: ${theme.colors.text};
    font-family: ${theme.fonts.body}, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    line-height: 1.6;
    margin: 0;
    padding: 0;
  }

  a {
    color: ${theme.colors.primary};
    text-decoration: none;
    transition: color 0.2s ease;
  }

  a:hover,
  a:focus {
    text-decoration: underline;
  }

  button {
    background: none;
    border: none;
    cursor: pointer;
    font-family: inherit;
  }

  input,
  textarea,
  select {
    font-family: inherit;
    font-size: 1rem;
  }

  ::selection {
    background-color: ${theme.colors.primary};
    color: white;
  }
`;

export default function GlobalStyles() {
  return <Global styles={globalStyles} />;
}
