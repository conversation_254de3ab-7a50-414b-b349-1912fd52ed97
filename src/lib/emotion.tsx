'use client';

import { useState } from 'react';

import { useServerInsertedHTML } from 'next/navigation';

import createCache from '@emotion/cache';
import { CacheProvider } from '@emotion/react';

// This implementation is based on the recommended approach for Next.js App Router
// https://github.com/emotion-js/emotion/issues/2928#issuecomment-**********
export default function EmotionProvider({ children }: Readonly<{ children: React.ReactNode }>) {
  const [{ cache, flush }] = useState(() => {
    const cache = createCache({ key: 'css' });

    // This is important for SSR and SSG
    cache.compat = true;

    // Create a function to flush the cache
    const prevInsert = cache.insert;
    let inserted: string[] = [];

    cache.insert = (...args) => {
      const serialized = args[1];
      if (cache.inserted[serialized.name] === undefined) {
        inserted.push(serialized.name);
      }

      return prevInsert(...args);
    };

    const flush = () => {
      const prevInserted = inserted;
      inserted = [];

      return prevInserted;
    };

    return { cache, flush };
  });

  useServerInsertedHTML(() => {
    const names = flush();
    if (names.length === 0) return null;

    let styles = '';
    for (const name of names) {
      styles += cache.inserted[name];
    }

    return (
      <style
        dangerouslySetInnerHTML={{ __html: styles }}
        data-emotion={`${cache.key} ${names.join(' ')}`}
      />
    );
  });

  return <CacheProvider value={cache}>{children}</CacheProvider>;
}
