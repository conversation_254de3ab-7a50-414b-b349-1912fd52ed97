import React from 'react';

type ImageProps = {
  src: string;
  alt?: string;
  width?: number | string;
  height?: number | string;
  priority?: boolean;
  loading?: 'eager' | 'lazy';
  unoptimized?: boolean;
  lazyBoundary?: string;
  placeholder?: 'blur' | 'empty';

  [key: string]: string | number | boolean | undefined;
};

export const imageMock = {
  __esModule: true,
  default: (props: ImageProps) => {
    const imgProps = { ...props };

    const booleanProps = ['priority', 'loading', 'unoptimized', 'lazyBoundary', 'placeholder'];

    booleanProps.forEach((prop: string) => {
      if (prop in imgProps && typeof imgProps[prop] === 'boolean') {
        imgProps[prop] = String(imgProps[prop]);
      }
    });

    return React.createElement('img', imgProps);
  },
};
