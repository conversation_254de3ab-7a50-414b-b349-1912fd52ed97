import React from 'react';

export const intlMock = {
  useTranslations: (namespace?: string) => (key: string, params?: Record<string, unknown>) => {
    if (namespace === 'footer' && key === 'copyright' && params?.year) {
      const year =
        typeof params.year === 'number' || typeof params.year === 'string'
          ? params.year
          : new Date().getFullYear();

      return `© ${year} Your Company. All rights reserved.`;
    }

    if (params) {
      let result = key;
      Object.entries(params).forEach(([paramKey, value]) => {
        result = result.replace(`{${paramKey}}`, String(value));
      });

      return result;
    }

    const translations: Record<string, Record<string, string>> = {
      header: {
        home: 'Home',
        about: 'About',
        contact: 'Contact',
      },
      footer: {
        resources: 'Resources',
        documentation: 'Documentation',
        learn: 'Learn',
        templates: 'Templates',
        github: 'GitHub',
        blog: 'Blog',
        nextjs: 'Next.js',
      },
      common: {
        more: 'More',
      },
    };

    if (namespace && translations[namespace]) {
      return translations[namespace][key] || key;
    }

    for (const ns in translations) {
      if (translations[ns][key]) {
        return translations[ns][key];
      }
    }

    return key;
  },
  useFormatter: () => ({
    dateTime: () => '01/01/2023',
    number: (num: number) => num.toString(),
    list: (items: unknown[]) => items.map(String).join(', '),
  }),
  NextIntlClientProvider: ({ children }: { children: React.ReactNode }) =>
    React.createElement(React.Fragment, null, children),
};
