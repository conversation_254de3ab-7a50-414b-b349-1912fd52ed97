type NextRouter = {
  push: jest.Mock;
  replace: jest.<PERSON><PERSON>;
  prefetch: jest.<PERSON>ck;
  back: jest.<PERSON>ck;
  pathname: string;
  query: Record<string, string>;
  asPath: string;
  events: {
    on: jest.Mock;
    off: jest.Mock;
    emit: jest.Mock;
  };
};

export const routerMock = {
  useRouter: (): NextRouter => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    pathname: '/',
    query: {},
    asPath: '/',
    events: {
      on: jest.fn(),
      off: jest.fn(),
      emit: jest.fn(),
    },
  }),
};
