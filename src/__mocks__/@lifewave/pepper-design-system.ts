import React from 'react';

// Mock for @lifewave/pepper-design-system
export const getTheme = (_mode: 'light' | 'dark') => ({
  palette: {
    primary: {
      main: '#0070f3',
    },
    secondary: {
      main: '#ff4081',
    },
    background: {
      default: '#ffffff',
    },
    text: {
      primary: '#333333',
    },
    error: {
      main: '#d32f2f',
    },
    success: {
      main: '#388e3c',
    },
  },
  typography: {
    fontFamily: 'var(--font-geist-sans)',
  },
  spacing: (factor: number) => `${factor * 8}px`,
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 960,
      lg: 1280,
      xl: 1920,
    },
  },
});

// Mock ThemeProvider component
export const ThemeProvider = ({ children }: { children: React.ReactNode; theme?: unknown }) => {
  return React.createElement('div', { 'data-testid': 'theme-provider' }, children);
};
