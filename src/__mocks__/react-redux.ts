import React from 'react';

// Mock for react-redux
export const useSelector = jest.fn();
export const useDispatch = jest.fn(() => jest.fn());
export const useStore = jest.fn(() => ({
  getState: jest.fn(),
  dispatch: jest.fn(),
  subscribe: jest.fn(),
}));

export const Provider = ({ children }: { children: React.ReactNode }) => {
  return React.createElement('div', { 'data-testid': 'redux-provider' }, children);
};

export const connect = () => (component: React.ComponentType) => component;
