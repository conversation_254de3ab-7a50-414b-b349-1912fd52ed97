# Redux Store Structure

Esta carpeta contiene toda la configuración y lógica de Redux Toolkit para el proyecto Pepper Frontend.

## 📁 Estructura

```
src/store/
├── api/                    # RTK Query API slices
│   └── apiSlice.ts        # API slice principal con endpoints
├── types/                 # Tipos TypeScript para Redux
│   └── api.types.ts      # Tipos para APIs y respuestas
├── hooks.ts              # Hooks tipados para Redux
├── Provider.tsx          # Provider de Redux para la aplicación
├── store.ts              # Configuración del store principal
└── README.md             # Esta documentación
```

## 🔧 Configuración

### Store Principal (`store.ts`)
- Configurado con Redux Toolkit
- Incluye RTK Query middleware
- Redux DevTools habilitado en desarrollo
- Configuración de serialización para RTK Query

### API Slice (`api/apiSlice.ts`)
- Configurado con RTK Query
- Base URL: `http://localhost:8080`
- Endpoints disponibles:
  - `getHelloWorld`: GET `/api/v1/hello`

### Hooks Tipados (`hooks.ts`)
- `useAppDispatch`: Hook tipado para dispatch
- `useAppSelector`: Hook tipado para selector

## 🚀 Uso

### En Componentes

```tsx
import { useGetHelloWorldQuery } from '@store/api/apiSlice';

export const MyComponent = () => {
  const { data, error, isLoading } = useGetHelloWorldQuery();
  
  if (isLoading) return <div>Cargando...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return <div>{data?.message}</div>;
};
```

### Configuración de Polling y Refetch

```tsx
const { data } = useGetHelloWorldQuery(undefined, {
  pollingInterval: 30000,      // Refetch cada 30 segundos
  refetchOnFocus: true,        // Refetch al enfocar la ventana
  refetchOnReconnect: true,    // Refetch al reconectar
});
```

## 🧪 Testing

Los componentes que usan Redux están mockeados en `src/__mocks__/react-redux.ts`.

### Ejemplo de Test

```tsx
// Mock del hook
jest.mock('@store/api/apiSlice', () => ({
  useGetHelloWorldQuery: jest.fn(),
}));

const mockUseGetHelloWorldQuery = jest.mocked(
  require('@store/api/apiSlice').useGetHelloWorldQuery
);

// En el test
mockUseGetHelloWorldQuery.mockReturnValue({
  data: { message: 'Hello World!' },
  error: undefined,
  isLoading: false,
  isError: false,
});
```

## 📝 Mejores Prácticas

1. **Tipos TypeScript**: Siempre define tipos para las respuestas de API
2. **Error Handling**: Usa `transformErrorResponse` para manejar errores consistentemente
3. **Caching**: RTK Query maneja el cache automáticamente
4. **Tags**: Usa `providesTags` e `invalidatesTags` para invalidación de cache
5. **Polling**: Usa con moderación para evitar sobrecarga del servidor

## 🔄 Agregar Nuevos Endpoints

1. Define los tipos en `types/api.types.ts`
2. Agrega el endpoint en `api/apiSlice.ts`
3. Exporta el hook generado
4. Usa el hook en tus componentes
