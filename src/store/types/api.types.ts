// API Response types
export interface HelloWorldResponse {
  message: string;
  timestamp?: string;
  version?: string;
}

// API Error types
export interface ApiError {
  message: string;
  status: number;
  data?: unknown;
}

// Base API response wrapper
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// API endpoints configuration
export const API_ENDPOINTS = {
  HELLO: '/api/v1/hello',
} as const;

// Base URL configuration
export const API_BASE_URL = 'http://localhost:8080';
