import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

import { API_BASE_URL, API_ENDPOINTS, HelloWorldResponse } from '@store/types/api.types';

// Define the API slice using RTK Query
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers) => {
      // Add any default headers here
      headers.set('Content-Type', 'application/json');
      return headers;
    },
  }),
  tagTypes: ['Hello'],
  endpoints: (builder) => ({
    // Get Hello World message
    getHelloWorld: builder.query<HelloWorldResponse, void>({
      query: () => ({
        url: API_ENDPOINTS.HELLO,
        method: 'GET',
      }),
      providesTags: ['Hello'],
      // Transform response if needed
      transformResponse: (response: HelloWorldResponse) => {
        return {
          ...response,
          timestamp: new Date().toISOString(),
        };
      },
      // Handle errors
      transformErrorResponse: (response) => {
        return {
          status: response.status,
          message: 'Failed to fetch hello world message',
          data: response.data,
        };
      },
    }),
  }),
});

// Export hooks for usage in functional components
export const { useGetHelloWorldQuery } = apiSlice;
