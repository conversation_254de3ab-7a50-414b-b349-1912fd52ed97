import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

import { API_BASE_URL, API_ENDPOINTS, HelloWorldResponse } from '../types/api.types';

// Define the API slice using RTK Query
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers) => {
      // Add any default headers here
      headers.set('Content-Type', 'application/json');
      return headers;
    },
  }),
  tagTypes: ['Hello'],
  endpoints: (builder) => ({
    // Get Hello World message
    getHelloWorld: builder.query<HelloWorldResponse, void>({
      query: () => ({
        url: API_ENDPOINTS.HELLO,
        method: 'GET',
      }),
      providesTags: ['Hello'],
      transformResponse: (response: any) => {
        // Si la respuesta es un string simple, lo convertimos al formato esperado
        if (typeof response === 'string') {
          return {
            message: response,
            timestamp: new Date().toISOString(),
          };
        }

        // Si ya es un objeto, lo devolvemos tal como está
        if (response && typeof response === 'object') {
          return {
            ...response,
            timestamp: response.timestamp || new Date().toISOString(),
          };
        }

        // Fallback por si la respuesta es de otro tipo
        return {
          message: String(response),
          timestamp: new Date().toISOString(),
        };
      },
    }),
  }),
});

// Export hooks for usage in functional components
export const { useGetHelloWorldQuery } = apiSlice;
