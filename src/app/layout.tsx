import { ReactNode } from 'react';

import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from 'next/font/google';
import { NextIntlClientProvider } from 'next-intl';
import { getLocale, getMessages } from 'next-intl/server';

import { Footer } from '@components/Footer/Footer';
import { Header } from '@components/Header/Header';
import { Main } from '@components/Main/Main';

import EmotionProvider from '@lib/emotion';
import { ReduxProvider } from '../store/Provider';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Pepper',
  description: 'The gateway to harnessing the power of artificial intelligence within Lifewave',
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  const locale = await getLocale();
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body className={`${geistSans.variable} ${geistMono.variable}`}>
        <ReduxProvider>
          <NextIntlClientProvider locale={locale} messages={messages}>
            <EmotionProvider>
              <Header />
              <Main>{children}</Main>
              <Footer />
            </EmotionProvider>
          </NextIntlClientProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
