name: CI/CD Pipeline

on:
  push:
    branches: [ main, dev ]
  pull_request:
    branches: [ main, dev ]
  workflow_dispatch:

jobs:
  commitlint:
    name: Validate Commit Messages
    uses: ./.github/workflows/_reusable-checks.yml
    with:
      task: commitlint
    if: github.event_name == 'pull_request'

  lint:
    name: Lint Code
    uses: ./.github/workflows/_reusable-checks.yml
    with:
      task: lint

  test:
    name: Run Unit Tests
    uses: ./.github/workflows/_reusable-checks.yml
    with:
      task: test

  build:
    name: Build Application
    needs: [ commitlint, lint, test ]
    uses: ./.github/workflows/_reusable-checks.yml
    with:
      task: build
