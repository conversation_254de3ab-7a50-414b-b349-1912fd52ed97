name: Reusable Task Workflow

on:
  workflow_call:
    inputs:
      task:
        required: true
        type: string

jobs:
  run-task:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - run: npm ci

      - name: Run Task
        run: |
          if [[ "${{ inputs.task }}" == "commitlint" ]]; then
            npx commitlint --from ${{ github.event.pull_request.base.sha }} --to ${{ github.event.pull_request.head.sha }} --verbose
          elif [[ "${{ inputs.task }}" == "lint" ]]; then
            npm run lint
          elif [[ "${{ inputs.task }}" == "test" ]]; then
            npm run test:coverage
          elif [[ "${{ inputs.task }}" == "build" ]]; then
            npm run build
          else
            echo \"Unknown task: ${{ inputs.task }}\"
            exit 1
          fi
