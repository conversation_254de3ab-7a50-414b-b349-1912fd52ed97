name: Release

on:
  push:
    branches:
      - main
      - dev

jobs:
  lint:
    name: Lint Code
    uses: ./.github/workflows/_reusable-checks.yml
    with:
      task: lint

  test:
    name: Run Unit Tests
    uses: ./.github/workflows/_reusable-checks.yml
    with:
      task: test

  build:
    name: Build Package
    uses: ./.github/workflows/_reusable-checks.yml
    needs: [lint, test]
    with:
      task: build

  release:
    name: Publish Release
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push'
    permissions:
      contents: write
      issues: write
      pull-requests: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'
          registry-url: 'https://npm.pkg.github.com'

      - name: Install dependencies
        run: npm ci

      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: npx semantic-release
