{"name": "pepper-frontend", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "npm run format && npm run lint:fix && npm run lint:style", "lint:check": "next lint", "lint:fix": "next lint --fix", "lint:style": "stylelint \"src/**/*.{ts,tsx}\" --fix", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:clear-cache": "jest --clear<PERSON>ache", "test:update-cache": "jest --updateSnapshot", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "prepare": "husky", "pre-commit": "npm run lint", "pre-push": "npm run test"}, "dependencies": {"@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@reduxjs/toolkit": "2.8.2", "next": "15.3.2", "next-intl": "4.1.0", "react": "19.1.0", "react-dom": "19.1.0", "react-redux": "9.2.0"}, "devDependencies": {"@commitlint/cli": "19.8.1", "@commitlint/config-conventional": "19.8.1", "@emotion/babel-plugin": "11.13.5", "@emotion/server": "11.11.0", "@eslint/eslintrc": "3.3.1", "@stylelint/postcss-css-in-js": "0.38.0", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@types/jest": "29.5.14", "@types/node": "22.15.21", "@types/react": "19.1.5", "@types/react-dom": "19.1.5", "eslint": "9.27.0", "eslint-config-next": "15.3.2", "eslint-plugin-import": "2.31.0", "eslint-plugin-prettier": "5.4.0", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-simple-import-sort": "12.1.1", "eslint-plugin-sonarjs": "3.0.2", "eslint-plugin-unused-imports": "4.1.4", "husky": "9.1.7", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "postcss-styled-syntax": "0.7.1", "prettier": "3.5.3", "prettier-plugin-sort-json": "4.1.1", "stylelint": "16.19.1", "stylelint-config-recommended": "16.0.0", "stylelint-config-standard": "38.0.0", "stylelint-config-styled-components": "0.1.1", "stylelint-order": "7.0.0", "stylelint-processor-styled-components": "1.10.0", "ts-node": "10.9.2", "typescript": "5.8.3", "typescript-eslint": "8.32.1"}}